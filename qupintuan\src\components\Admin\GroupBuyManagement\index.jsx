// src/components/Admin/GroupBuyManagement/index.jsx
import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { queryContractBalances, querySystemOverview } from '@/utils/contractBalanceQueryFixed';
import { createPublicClient, http, formatUnits } from 'viem';
import { bscTestnet } from 'viem/chains';
import { CONTRACT_ADDRESSES, ABIS } from '@/contracts';
import './index.css';

export default function GroupBuyManagement() {
  const [groupBuyStats, setGroupBuyStats] = useState({
    qptBalance: '0.000',
    usdtBalance: '0.000',
    bnbBalance: '0.000',
    totalRooms: '0',
    activeRooms: '0',
    completedRooms: '0',
    totalParticipants: '0'
  });
  const [isLoadingStats, setIsLoadingStats] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState(null);

  // 查询拼团房间统计数据
  const queryGroupBuyRoomStats = async () => {
    try {
      const publicClient = createPublicClient({
        chain: bscTestnet,
        transport: http()
      });

      const groupBuyRoomAddress = CONTRACT_ADDRESSES[97].GroupBuyRoom;
      const queryContractAddress = CONTRACT_ADDRESSES[97].SimpleQueryContractV3;

      // 查询合约暂时不可用，直接使用备用方案

      // 备用方案：直接查询GroupBuyRoom合约
      const totalRooms = await publicClient.readContract({
        address: groupBuyRoomAddress,
        abi: ABIS.GroupBuyRoom,
        functionName: 'totalRooms'
      }).catch(() => 0n);

      // 统计房间状态和参与者
      let activeRooms = 0;
      let completedRooms = 0;
      let totalParticipants = 0;

      const maxRoomsToQuery = Math.min(Number(totalRooms), 20); // 减少查询数量提高性能

      // 注意：房间ID从0开始，totalRooms返回的是nextRoomId（下一个要分配的ID）
      for (let roomId = 0; roomId < maxRoomsToQuery; roomId++) {
        try {
          // 使用getRoom函数一次性获取所有房间信息
          const roomInfo = await publicClient.readContract({
            address: groupBuyRoomAddress,
            abi: ABIS.GroupBuyRoom,
            functionName: 'getRoom',
            args: [BigInt(roomId)]
          });

          const creator = roomInfo[0];
          const participants = roomInfo[2] || [];
          const isClosed = roomInfo[4];

          // 只统计有效房间（有创建者且tier > 0）
          if (creator !== '0x0000000000000000000000000000000000000000' && Number(roomInfo[1]) > 0) {
            totalParticipants += participants.length;

            if (isClosed) {
              completedRooms++;
            } else {
              activeRooms++;
            }
          }
        } catch (error) {
          // 忽略单个房间查询失败（移除详细错误日志）
        }
      }

      return {
        totalRooms: Number(totalRooms),
        activeRooms,
        completedRooms,
        totalParticipants
      };
    } catch (error) {
      console.error('查询拼团房间统计失败:', error);
      return {
        totalRooms: 0,
        activeRooms: 0,
        completedRooms: 0,
        totalParticipants: 0
      };
    }
  };

  // 加载拼团统计数据
  const loadGroupBuyStats = async () => {
    setIsLoadingStats(true);
    try {
      const [balances, overview, roomStats] = await Promise.all([
        queryContractBalances(),
        querySystemOverview(),
        queryGroupBuyRoomStats()
      ]);

      const groupBuyBalance = balances?.groupbuyroom || {};
      const dailyGroupBuyStats = overview?.dailyStats?.groupBuyRoom || {};



      setGroupBuyStats({
        usdtBalance: groupBuyBalance.usdt || '0.000',
        bnbBalance: groupBuyBalance.bnb || '0.000',
        totalRooms: roomStats.totalRooms.toString(),
        activeRooms: roomStats.activeRooms.toString(),
        completedRooms: roomStats.completedRooms.toString(),
        totalParticipants: roomStats.totalParticipants.toString()
        // 移除硬编码的今日收入和转出数据，因为合约中无法查询到真实数据
      });

      setLastUpdateTime(new Date());
    } catch (error) {
      console.error('加载拼团统计失败:', error);
      toast.error('加载拼团统计失败: ' + error.message);
    } finally {
      setIsLoadingStats(false);
    }
  };

  // 组件挂载时加载统计数据
  useEffect(() => {
    loadGroupBuyStats();
  }, []);

  // 格式化时间
  const formatTime = (date) => {
    if (!date) return '暂无数据';
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <div className="groupbuy-management">
      <div className="management-header">
        <div className="header-content">
          <h2>🏪 拼团管理</h2>
          <p>管理拼团房间和用户参与</p>
        </div>
        <div className="header-actions">
          <span className="last-update">
            最后更新: {formatTime(lastUpdateTime)}
          </span>
          <button
            className="refresh-btn"
            onClick={loadGroupBuyStats}
            disabled={isLoadingStats}
          >
            {isLoadingStats ? '🔄 刷新中...' : '🔄 刷新统计'}
          </button>
        </div>
      </div>

      {/* 拼团统计信息 */}
      <div className="groupbuy-stats-section">
        <h3>📊 拼团统计</h3>
        <div className="stats-grid">
          <div className="stat-card success">
            <div className="stat-icon">💵</div>
            <div className="stat-content">
              <div className="stat-value">{groupBuyStats.usdtBalance}</div>
              <div className="stat-label">USDT余额</div>
            </div>
          </div>
          <div className="stat-card info">
            <div className="stat-icon">🏠</div>
            <div className="stat-content">
              <div className="stat-value">{groupBuyStats.totalRooms}</div>
              <div className="stat-label">总房间数</div>
            </div>
          </div>
          <div className="stat-card warning">
            <div className="stat-icon">🔥</div>
            <div className="stat-content">
              <div className="stat-value">{groupBuyStats.activeRooms}</div>
              <div className="stat-label">活跃房间</div>
            </div>
          </div>
          <div className="stat-card secondary">
            <div className="stat-icon">✅</div>
            <div className="stat-content">
              <div className="stat-value">{groupBuyStats.completedRooms}</div>
              <div className="stat-label">已完成房间</div>
            </div>
          </div>
          <div className="stat-card accent">
            <div className="stat-icon">👥</div>
            <div className="stat-content">
              <div className="stat-value">{groupBuyStats.totalParticipants}</div>
              <div className="stat-label">总参与人数</div>
            </div>
          </div>
          <div className="stat-card gas">
            <div className="stat-icon">⚡</div>
            <div className="stat-content">
              <div className="stat-value">{parseFloat(groupBuyStats.bnbBalance).toFixed(3)} BNB</div>
              <div className="stat-label">Gas费余额</div>
            </div>
          </div>
        </div>
      </div>

      {/* 拼团操作 */}
      <div className="groupbuy-operations">
        <h3>⚡ 拼团操作</h3>
        <div className="operation-buttons">
          <button className="operation-btn primary">
            🏠 查看房间列表
          </button>
          <button className="operation-btn success">
            📊 导出拼团报告
          </button>
          <button className="operation-btn info">
            🔍 查询房间信息
          </button>
          <button className="operation-btn warning">
            ⚙️ 拼团设置
          </button>
          <button className="operation-btn secondary">
            👥 参与者管理
          </button>
          <button className="operation-btn accent">
            🎯 房间统计
          </button>
        </div>
      </div>




    </div>
  );
}
