// 测试积分精度修复
// 这个脚本用于验证积分转账功能的精度修复是否正确

import { parseUnits, formatUnits } from 'ethers';
import { parsePointsInput, formatPoints, POINTS_DECIMALS } from './src/utils/pointsFormatter.js';

console.log('🧪 积分精度修复测试');
console.log('='.repeat(50));

// 测试用例
const testCases = [
  { input: '10', description: '转账10积分' },
  { input: '1', description: '转账1积分' },
  { input: '0.5', description: '转账0.5积分' },
  { input: '100.123456', description: '转账100.123456积分（6位小数）' },
];

console.log(`📊 积分精度常量: ${POINTS_DECIMALS} 位小数`);
console.log('');

testCases.forEach((testCase, index) => {
  console.log(`测试 ${index + 1}: ${testCase.description}`);
  console.log(`输入: ${testCase.input}`);
  
  try {
    // 使用修复后的函数
    const correctAmount = parsePointsInput(testCase.input);
    const correctFormatted = formatPoints(correctAmount);
    
    // 使用错误的方法（修复前）
    const wrongAmount = parseUnits(testCase.input, 0);
    const wrongFormatted = formatUnits(wrongAmount, 0);
    
    console.log(`✅ 正确方法 (6位精度): ${correctAmount.toString()} wei -> ${correctFormatted} 积分`);
    console.log(`❌ 错误方法 (0位精度): ${wrongAmount.toString()} wei -> ${wrongFormatted} 积分`);
    
    // 计算差异
    const ratio = Number(correctAmount) / Number(wrongAmount);
    console.log(`📈 精度差异: ${ratio.toExponential(2)} 倍`);
    
  } catch (error) {
    console.log(`❌ 测试失败: ${error.message}`);
  }
  
  console.log('-'.repeat(30));
});

console.log('');
console.log('🎯 修复总结:');
console.log('- 修复前: parseUnits(transferAmount, 0) - 使用0位精度');
console.log('- 修复后: parsePointsInput(transferAmount) - 使用6位精度');
console.log('- 结果: 转账10积分时，接收方将收到10积分而不是0.00001积分');
