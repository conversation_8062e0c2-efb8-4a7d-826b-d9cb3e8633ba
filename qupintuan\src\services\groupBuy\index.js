// src/services/groupBuy/index.js
// 拼团服务主入口文件

// 导出核心业务逻辑
export {
  createAndLock,
  fetchTotalRooms,
  fetchRoom,
  joinRoom,
  claimReward
} from './core';

// 导出验证函数
export {
  throwError,
  validateChainId,
  VALIDATE_TIER_AMOUNT,
  parseTierAmount,
  validateSigner,
  validateRoomData,
  validateParticipantData,
  validateAddress,
  validateTxHash
} from './validation';

// 导出交易处理函数
export {
  executeTransaction,
  retryTransaction,
  checkTransactionStatus,
  estimateGas,
  batchTransactions,
  waitForConfirmations
} from './transaction';

// 导出工具函数
export {
  recalculateRoomStatus,
  formatRoomStatus,
  calculateRemainingTime,
  canUserJoinRoom,
  canUserClaimReward,
  formatAddress,
  formatTimestamp,
  getRelativeTime,
  generateShareLink,
  validateRoomData as validateRoomDataIntegrity
} from './utils';

// 导出常量
export {
  ERROR_CODES,
  ROOM_STATUS,
  TIER_LEVELS,
  NETWORK_CONFIG,
  CONTRACT_CONFIG,
  TIME_CONSTANTS,
  LIMITS,
  PRECISION,
  EVENTS,
  STORAGE_KEYS
} from './constants';

// 创建一个新的服务类来替代原来的 newGroupBuyService
class GroupBuyService {
  constructor() {
    this.name = 'GroupBuyService';
    this.version = '2.0.0';
  }

  // 核心方法
  async createAndLock(signer, tierAmountStr) {
    try {
      const { createAndLock } = await import('./core');
      const result = await createAndLock(signer, tierAmountStr);
      return result;
    } catch (error) {
      console.error('createAndLock 失败', error);
      throw error;
    }
  }

  async fetchTotalRooms(chainId) {
    const { fetchTotalRooms } = await import('./core');
    return fetchTotalRooms(chainId);
  }

  async fetchRoom(chainId, roomId) {
    const { fetchRoom } = await import('./core');
    return fetchRoom(chainId, roomId);
  }

  async joinRoom(params) {
    const { joinRoom } = await import('./core');
    return joinRoom(params);
  }

  async claimReward(params) {
    const { claimReward } = await import('./core');
    return claimReward(params);
  }

  async fetchRooms({ chainId, currentUserAddress, isMyRoomsPage, page = 1, pageSize = 10 }) {
    try {
      // 动态导入必要的模块
      const { createPublicClient, http } = await import('viem');
      const { bscTestnet } = await import('viem/chains');
      const { CONTRACT_ADDRESSES, ABIS } = await import('@/contracts');
      const { recalculateRoomStatus } = await import('./utils');

      const publicClient = createPublicClient({
        chain: bscTestnet,
        transport: http()
      });

      const groupBuyAddress = CONTRACT_ADDRESSES[chainId || 97].GroupBuyRoom;

      // 获取房间总数
      const totalRooms = await publicClient.readContract({
        address: groupBuyAddress,
        abi: ABIS.GroupBuyRoom,
        functionName: 'totalRooms'
      });

      if (totalRooms === undefined) {
        throw new Error('totalRooms函数返回undefined，可能是合约地址错误或网络问题');
      }

      const totalRoomsNum = Number(totalRooms);

      if (totalRoomsNum === 0) {
        return {
          rooms: [],
          totalCount: 0,
          currentPage: 1,
          totalPages: 0,
          pageSize: roomsPerPage,
          hasNextPage: false,
          hasPrevPage: false
        };
      }

      const rooms = [];

      // 分页配置：每页10个房间，倒序查询（最新房间在前）
      const currentPage = page; // 当前页码
      const roomsPerPage = pageSize; // 每页房间数量，默认10个

      // 根据页面类型决定获取房间的数量
      let estimatedRoomsToFetch;
      let endRoomId;
      let startRoomId;

      if (isMyRoomsPage) {
        // "我的房间"页面：获取所有房间来找到用户相关的房间
        estimatedRoomsToFetch = totalRoomsNum; // 获取所有房间
        endRoomId = totalRoomsNum - 1;
        startRoomId = 0; // 从第一个房间开始
      } else {
        // 主页面：只获取有限数量的房间来显示进行中的房间
        estimatedRoomsToFetch = roomsPerPage * 3; // 获取3倍数量，确保有足够的进行中房间
        endRoomId = totalRoomsNum - (currentPage - 1) * roomsPerPage - 1;
        startRoomId = Math.max(0, endRoomId - estimatedRoomsToFetch + 1);
      }



      // 批量获取房间信息，提高性能
      const batchSize = 25; // 优化批次大小，平衡性能和稳定性
      const roomsToFetch = endRoomId - startRoomId + 1;

      for (let i = 0; i < roomsToFetch; i += batchSize) {
        const batch = [];
        const batchStartId = startRoomId + i;
        const batchEndId = Math.min(startRoomId + i + batchSize - 1, endRoomId);



        for (let j = batchStartId; j <= batchEndId; j++) {
          batch.push(
            publicClient.readContract({
              address: groupBuyAddress,
              abi: ABIS.GroupBuyRoom,
              functionName: 'getRoomDetails',
              args: [BigInt(j)]
            }).then(roomData => ({ id: j, data: roomData }))
            .catch(error => {
              console.warn(`获取房间 ${j} 失败:`, error.message);
              return null;
            })
          );
        }

        const batchResults = await Promise.all(batch);

        for (const result of batchResults) {
          if (result && result.data) {
            try {
              // 检查房间是否有效（有创建者）
              if (!result.data[0] || result.data[0] === '0x0000000000000000000000000000000000000000') {
                continue;
              }

              // getRoomDetails 返回的数据结构（16个字段）：
              // [0] creator, [1] tier, [2] participants, [3] createTime, [4] isClosed,
              // [5] isSuccessful, [6] winnerIndex, [7] winner, [8] subsidyPer,
              // [9] creatorCommission, [10] systemFee, [11] systemFeeDistributed,
              // [12] endTime, [13] readyForWinner, [14] lotteryTxHash, [15] lotteryTimestamp
              const room = {
                id: result.id,
                creator: result.data[0],
                tier: Number(result.data[1]),
                participants: result.data[2] || [],
                createdAt: Number(result.data[3]),
                isClosed: result.data[4],
                isSuccessful: result.data[5],
                winnerIndex: result.data[6],
                winner: result.data[7],
                subsidyPer: Number(result.data[8]),
                creatorCommission: Number(result.data[9]),
                systemFee: Number(result.data[10]),
                systemFeeDistributed: result.data[11],
                endTime: Number(result.data[12]),
                readyForWinner: result.data[13],
                lotteryTxHash: result.data[14],
                lotteryTimestamp: Number(result.data[15]),
                participantsCount: result.data[2] ? result.data[2].length : 0,
                maxParticipants: 8,
                isCompleted: result.data[4] || result.data[5],
                lotteryInfo: result.data[13] ? {
                  winner: result.data[7],
                  lotteryTxHash: result.data[14],
                  lotteryTimestamp: result.data[15]
                } : null
              };

              // 重新计算房间状态
              const roomWithStatus = recalculateRoomStatus(room);



              // 如果是"我的房间"页面，只返回用户相关的房间
              if (isMyRoomsPage && currentUserAddress) {
                const isUserRoom = room.creator?.toLowerCase() === currentUserAddress.toLowerCase() ||
                                 room.participants?.some(p => p.toLowerCase() === currentUserAddress.toLowerCase());
                if (isUserRoom) {
                  rooms.push(roomWithStatus);
                }
              } else {
                // 主页面需要验证QPT锁仓状态
                rooms.push(roomWithStatus);
              }
            } catch (error) {
              console.warn(`处理房间 ${result.id} 数据失败:`, error.message);
            }
          }
        }
      }

      // 按创建时间倒序排列，最新的房间在前面
      rooms.sort((a, b) => (b.createdAt || 0) - (a.createdAt || 0));

      // 增强房间数据，确保开奖信息持久显示
      let enhancedRooms = rooms;
      try {
        const { enhanceRoomsWithLotteryInfo } = await import('@/utils/lotteryInfoPersistence');
        enhancedRooms = enhanceRoomsWithLotteryInfo(rooms);

        const enhancedCount = enhancedRooms.filter(room => room._lotteryInfoEnhanced).length;
      } catch (error) {
        console.warn('批量增强房间开奖信息失败:', error);
      }

      // 对所有页面都进行房间验证和过滤，确保只显示正常的房间
      try {
        // 动态导入房间验证服务
        const { validateRoomsQPTLock } = await import('@/services/roomValidationService');

        let roomsToValidate;

        if (isMyRoomsPage) {
          // "我的房间"页面：不过滤房间状态，保留所有房间（包括满员、已关闭、已过期的房间）
          // 让用户可以查看和操作所有相关的房间
          roomsToValidate = enhancedRooms; // 使用所有获取到的房间
        } else {
          // 主页面：只显示进行中的房间
          const { recalculateRoomStatus } = await import('./utils');
          const activeRooms = enhancedRooms.filter(room => {
            const roomWithStatus = recalculateRoomStatus(room);
            // 只保留进行中的房间
            return roomWithStatus.status === 'in_progress';
          });
          // 取前N个进行中的房间（按页面大小限制）
          roomsToValidate = activeRooms.slice(0, roomsPerPage);
        }

        // 批量验证房间的QPT锁仓状态，过滤掉无效房间
        const validatedRooms = await validateRoomsQPTLock(roomsToValidate, {
          filterInvalid: true, // 过滤掉无效房间
          maxConcurrent: 10,   // 增加并发数量，提高验证速度
          isMyRoomsPage        // 传递页面类型，用于不同的过滤策略
        });

        // 计算分页信息
        if (isMyRoomsPage) {
          // "我的房间"页面：显示所有用户相关的房间
          const hasMoreRooms = roomsToValidate.length > validatedRooms.length;
          const estimatedTotalRooms = hasMoreRooms ?
            (currentPage * roomsPerPage + 1) : // 如果还有更多，估算至少还有1个
            ((currentPage - 1) * roomsPerPage + validatedRooms.length); // 如果没有更多，精确计算

          const estimatedTotalPages = Math.ceil(estimatedTotalRooms / roomsPerPage);

          return {
            rooms: validatedRooms,
            totalCount: estimatedTotalRooms,
            currentPage,
            totalPages: estimatedTotalPages,
            pageSize: roomsPerPage,
            hasNextPage: hasMoreRooms,
            hasPrevPage: currentPage > 1,
            roomsInCurrentPage: validatedRooms.length,
            note: '显示所有相关房间（包括已完成、已过期的房间）'
          };
        } else {
          // 主页面：只显示进行中的房间
          const hasMoreActiveRooms = roomsToValidate.length > validatedRooms.length;
          const estimatedTotalActiveRooms = hasMoreActiveRooms ?
            (currentPage * roomsPerPage + 1) : // 如果还有更多，估算至少还有1个
            ((currentPage - 1) * roomsPerPage + validatedRooms.length); // 如果没有更多，精确计算

          const estimatedTotalPages = Math.ceil(estimatedTotalActiveRooms / roomsPerPage);

          return {
            rooms: validatedRooms,
            totalCount: estimatedTotalActiveRooms, // 进行中房间的估算总数
            currentPage,
            totalPages: estimatedTotalPages,
            pageSize: roomsPerPage,
            hasNextPage: hasMoreActiveRooms, // 基于实际获取的房间数量判断
            hasPrevPage: currentPage > 1,
            roomsInCurrentPage: validatedRooms.length,
            note: '只显示进行中的房间'
          };
        }
      } catch (error) {
        console.warn('房间验证失败，返回增强后的房间列表:', error);
        // 验证失败时返回进行中的房间列表，避免影响用户体验
        const { recalculateRoomStatus: fallbackRecalculateRoomStatus } = await import('./utils');
        const fallbackActiveRooms = enhancedRooms.filter(room => {
          const roomWithStatus = fallbackRecalculateRoomStatus(room);
          return roomWithStatus.status === 'in_progress';
        }).slice(0, roomsPerPage);

        const hasMoreRooms = fallbackActiveRooms.length === roomsPerPage;
        const estimatedTotal = hasMoreRooms ?
          (currentPage * roomsPerPage + 1) :
          ((currentPage - 1) * roomsPerPage + fallbackActiveRooms.length);

        return {
          rooms: fallbackActiveRooms,
          totalCount: estimatedTotal, // 进行中房间的估算总数
          currentPage,
          totalPages: Math.ceil(estimatedTotal / roomsPerPage),
          pageSize: roomsPerPage,
          hasNextPage: hasMoreRooms,
          hasPrevPage: currentPage > 1,
          roomsInCurrentPage: fallbackActiveRooms.length,
          note: '只显示进行中的房间（验证失败备用方案）'
        };
      }
    } catch (error) {
      console.error('获取房间列表失败:', error);
      throw error;
    }
  }

  // 工具方法
  async recalculateRoomStatus(room) {
    const { recalculateRoomStatus } = await import('./utils');
    return recalculateRoomStatus(room);
  }

  async formatRoomStatus(status) {
    const { formatRoomStatus } = await import('./utils');
    return formatRoomStatus(status);
  }

  async calculateRemainingTime(createdAt) {
    const { calculateRemainingTime } = await import('./utils');
    return calculateRemainingTime(createdAt);
  }

  async canUserJoinRoom(room, userAddress) {
    const { canUserJoinRoom } = await import('./utils');
    return canUserJoinRoom(room, userAddress);
  }

  async canUserClaimReward(room, userAddress) {
    const { canUserClaimReward } = await import('./utils');
    return canUserClaimReward(room, userAddress);
  }

  // 验证方法
  async validateRoomData(room) {
    const { validateRoomData } = await import('./validation');
    return validateRoomData(room);
  }

  async validateAddress(address) {
    const { validateAddress } = await import('./validation');
    return validateAddress(address);
  }

  // 交易方法
  async executeTransaction(apiCall, txName) {
    const { executeTransaction } = await import('./transaction');
    return executeTransaction(apiCall, txName);
  }

  async retryTransaction(transactionFn, maxRetries, delay) {
    const { retryTransaction } = await import('./transaction');
    return retryTransaction(transactionFn, maxRetries, delay);
  }
}

// 创建服务实例
const groupBuyService = new GroupBuyService();

// 导出服务实例（保持向后兼容）
export { groupBuyService as newGroupBuyService };
export default groupBuyService;
