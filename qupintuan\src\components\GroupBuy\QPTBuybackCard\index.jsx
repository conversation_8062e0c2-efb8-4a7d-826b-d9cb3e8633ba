import React, { useState } from 'react'
import { createPublicClient, createWalletClient, custom, http } from 'viem'
import { bscTestnet } from 'viem/chains'
import { toast } from 'react-hot-toast'
import { formatAddress } from '@/utils/addressFormatter'
import Countdown from '@/components/Countdown'
import WinnerRewardInfo from '../WinnerRewardInfo'
import { checkQptRefundStatus, verifyQptClaimSuccess } from '@/services/qptBuybackService'
import './QPTBuybackCard.css'

const QPTBuybackCard = ({ room, isCreator, account, onExpireRoom, isProcessing }) => {
  const [isClaimingReward, setIsClaimingReward] = useState(false)

  // 格式化创建时间
  const formatCreateTime = (timestamp) => {
    if (!timestamp) return '暂无'
    const date = new Date(timestamp * 1000)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  // 处理QPT回购房间领取奖励（统一处理所有角色）
  const handleQptClaimReward = async () => {
    if (!account) {
      toast.error('请先连接钱包')
      return
    }

    const roomId = room.id

    setIsClaimingReward(true)
    try {
      const { CONTRACT_ADDRESSES, ABIS } = await import('@/contracts')

      const walletClient = createWalletClient({
        chain: bscTestnet,
        transport: custom(window.ethereum)
      })

      const publicClient = createPublicClient({
        chain: bscTestnet,
        transport: http('https://bsc-testnet.public.blastapi.io')
      })

      const buybackAddress = CONTRACT_ADDRESSES[97].QPTBuyback

      // 检查房间状态和用户角色
      const isWinner = account?.toLowerCase() === room.winner?.toLowerCase()
      const isParticipant = room.participants.includes(account)
      const hasWinner = room.winner && room.winner !== '******************************************'
      const isExpired = Math.floor(Date.now() / 1000) > room.deadline

      let functionName, successMessage

      if (!hasWinner && isExpired) {
        // 过期房间退款
        const { canRefund, refundType, reason } = await checkQptRefundStatus(roomId, account)

        if (!canRefund) {
          toast.error(`无法退款: ${reason}`)
          setIsClaimingReward(false)
          return
        }

        if (refundType === 1) {
          functionName = 'refundFailed'
        } else if (refundType === 2) {
          functionName = 'refundSuccess'
        } else if (refundType === 3) {
          functionName = 'refundExpired'
        } else {
          toast.error('未知的退款类型')
          setIsClaimingReward(false)
          return
        }

        successMessage = '🎉 成功申请退款！'
      } else if (hasWinner && !isWinner && isParticipant) {
        // 非获奖者领取本金+补贴
        const { canRefund, refundType, reason } = await checkQptRefundStatus(roomId, account)

        if (!canRefund) {
          toast.error(`无法领取: ${reason}`)
          setIsClaimingReward(false)
          return
        }

        if (refundType === 2) {
          functionName = 'refundSuccess'
        } else {
          toast.error('当前状态无法领取本金+补贴')
          setIsClaimingReward(false)
          return
        }

        successMessage = '🎉 成功领取本金+补贴！'
      } else if (hasWinner && isWinner) {
        // 获奖者领取奖励
        functionName = 'claimReward'
        successMessage = '🎉 成功领取奖励！'
      } else {
        toast.error('当前状态无法进行任何操作')
        setIsClaimingReward(false)
        return
      }

      // 执行合约调用
      const txHash = await walletClient.writeContract({
        address: buybackAddress,
        abi: ABIS.QPTBuyback,
        functionName,
        args: [BigInt(roomId)],
        account
      })

      toast.success('交易已提交，等待确认...')

      // 等待交易确认
      await publicClient.waitForTransactionReceipt({ hash: txHash })

      // 验证操作成功
      const rewardType = functionName.includes('refund') ? 'refund' : 'reward'
      await verifyQptClaimSuccess(roomId, account, rewardType)

      toast.success(successMessage)

    } catch (error) {
      console.error('❌ [QPTBuyback] 领取操作失败:', error)
      toast.error('操作失败: ' + (error.message || '未知错误'))
    } finally {
      setIsClaimingReward(false)
    }
  }

  // 渲染房间状态
  const renderRoomStatus = () => {
    const isWinner = account?.toLowerCase() === room.winner?.toLowerCase()
    const isParticipant = room.participants.includes(account)
    const hasWinner = room.winner && room.winner !== '******************************************'
    const isExpired = Math.floor(Date.now() / 1000) > room.deadline

    if (!hasWinner && !isExpired) {
      return <div className="room-status-info waiting">⏳ 等待开奖</div>
    }

    if (!hasWinner && isExpired) {
      return (
        <div className="room-status-info expired">
          ⏰ 房间已过期
          {isParticipant && (
            <button
              className="claim-btn refund"
              onClick={handleQptClaimReward}
              disabled={isClaimingReward}
            >
              {isClaimingReward ? '申请中...' : '💰 申请退款'}
            </button>
          )}
        </div>
      )
    }

    if (hasWinner && isWinner) {
      return <WinnerRewardInfo room={room} account={account} />
    }

    if (hasWinner && !isWinner && isParticipant) {
      return (
        <div className="room-status-info participant">
          😔 未中奖，可领取本金+补贴
          <button
            className="claim-btn subsidy"
            onClick={handleQptClaimReward}
            disabled={isClaimingReward}
          >
            {isClaimingReward ? '领取中...' : '💰 领取本金+补贴'}
          </button>
        </div>
      )
    }

    if (hasWinner && !isParticipant) {
      return (
        <div className="room-status-info completed">
          🎉 开奖完成，赢家: {formatAddress(room.winner)}
        </div>
      )
    }

    return null
  }

  // 渲染过期处理按钮
  const renderExpireButton = () => {
    const hasWinner = room.winner && room.winner !== '******************************************'
    const isExpired = Math.floor(Date.now() / 1000) > room.deadline
    const isParticipant = room.participants.includes(account)

    if (!hasWinner && isExpired && isParticipant) {
      return (
        <button
          className="expire-btn"
          onClick={() => onExpireRoom(room.id)}
          disabled={isProcessing}
        >
          {isProcessing ? '处理中...' : '🔧 处理过期房间'}
        </button>
      )
    }

    return null
  }

  return (
    <div className="qpt-buyback-card">
      <div className="card-header">
        <div className="room-info">
          <h3 className="room-title">QPT回购房间 #{room.id}</h3>
          <div className="room-tier">100 QPT</div>
        </div>
        <div className="room-meta">
          <span className="creator-label">发起人:</span>
          <span className="creator-address">{formatAddress(room.creator || '******************************************')}</span>
          {isCreator && <span className="creator-badge">我创建的</span>}
        </div>
      </div>

      <div className="card-body">
        <div className="room-details">
          <div className="detail-row">
            <span className="detail-label">参与:</span>
            <span className="detail-value">{room.participants?.length || 0} / 8</span>
          </div>
          <div className="detail-row">
            <span className="detail-label">创建:</span>
            <span className="detail-value">{formatCreateTime(room.createdAt)}</span>
          </div>
          {room.lockedBuybackAmount && (
            <div className="detail-row">
              <span className="detail-label">档位:</span>
              <span className="detail-value">
                {(Number(room.lockedBuybackAmount) / 1e18).toFixed(0)} QPT
              </span>
            </div>
          )}
        </div>

        <div className="countdown-section">
          <div className="countdown-wrapper">
            <Countdown
              targetTime={room.deadline * 1000}
              className="room-countdown"
              onComplete={() => {}}
            />
          </div>
        </div>

        {renderRoomStatus()}
        {renderExpireButton()}
      </div>
    </div>
  )
}

export default QPTBuybackCard
