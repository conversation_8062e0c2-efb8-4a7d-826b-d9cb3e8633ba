// 用户交易历史查询
import { formatUnits } from 'viem'
import { CONTRACT_ADDRESSES, ABIS } from '@/contracts'
import { publicClient } from './contractBalanceCore.js'

/**
 * 查询用户的交易历史记录
 */
export async function queryUserTransactionHistory(userAddress) {
  try {
    const groupBuyRoomAddress = CONTRACT_ADDRESSES[97].GroupBuyRoom
    const qptLockerAddress = CONTRACT_ADDRESSES[97].QPTLocker
    const pointsManagementAddress = CONTRACT_ADDRESSES[97].PointsManagement

    // 查询用户参与的所有房间
    const userRooms = await queryUserAllRooms(userAddress)

    // 查询用户的积分转账记录
    const pointsTransfers = await queryUserPointsTransfers(userAddress)

    // 查询用户的QPT锁仓记录
    const qptLockHistory = await queryUserQPTLockHistory(userAddress)

    return {
      userAddress,
      roomHistory: userRooms,
      pointsTransfers,
      qptLockHistory,
      timestamp: new Date()
    }
  } catch (error) {
    console.error('查询用户交易历史失败:', error)
    throw new Error(`查询用户交易历史失败: ${error.message}`)
  }
}

/**
 * 查询用户参与的所有房间
 */
export async function queryUserAllRooms(userAddress) {
  try {
    const groupBuyRoomAddress = CONTRACT_ADDRESSES[97].GroupBuyRoom

    // 获取总房间数
    const totalRooms = await publicClient.readContract({
      address: groupBuyRoomAddress,
      abi: ABIS.GroupBuyRoom,
      functionName: 'roomCounter',
      args: []
    })

    const userRooms = []
    const roomCount = Number(totalRooms)

    // 检查每个房间
    for (let i = 0; i < roomCount; i++) {
      try {
        const [hasJoined, roomInfo] = await Promise.all([
          publicClient.readContract({
            address: groupBuyRoomAddress,
            abi: ABIS.GroupBuyRoom,
            functionName: 'hasJoined',
            args: [BigInt(i), userAddress]
          }),
          publicClient.readContract({
            address: groupBuyRoomAddress,
            abi: ABIS.GroupBuyRoom,
            functionName: 'getRoom',
            args: [BigInt(i)]
          })
        ])

        if (hasJoined && roomInfo) {
          userRooms.push({
            roomId: i,
            creator: roomInfo[0],
            tier: formatUnits(roomInfo[1], 6),
            participants: roomInfo[2],
            createTime: new Date(Number(roomInfo[3]) * 1000),
            isClosed: roomInfo[4],
            isSuccessful: roomInfo[5],
            isCreator: roomInfo[0].toLowerCase() === userAddress.toLowerCase(),
            participantCount: roomInfo[2].length
          })
        }
      } catch (error) {
        console.warn(`查询房间 ${i} 失败:`, error.message)
      }
    }

    return userRooms
  } catch (error) {
    console.error('查询用户房间历史失败:', error)
    throw new Error(`查询用户房间历史失败: ${error.message}`)
  }
}

/**
 * 查询用户的积分转账记录（简化版，实际需要事件日志）
 */
export async function queryUserPointsTransfers(userAddress) {
  try {
    // 由于RPC限制，这里返回模拟数据
    // 实际应该查询Transfer事件日志
    return {
      sent: [], // 发送的转账
      received: [], // 接收的转账
      note: '积分转账记录需要事件日志查询，当前受RPC限制'
    }
  } catch (error) {
    console.error('查询积分转账记录失败:', error)
    return { sent: [], received: [], note: '查询失败' }
  }
}

/**
 * 查询用户的QPT锁仓历史
 */
export async function queryUserQPTLockHistory(userAddress) {
  try {
    const qptLockerAddress = CONTRACT_ADDRESSES[97].QPTLocker

    // 查询节点质押
    const nodeStaking = await publicClient.readContract({
      address: qptLockerAddress,
      abi: ABIS.QPTLocker,
      functionName: 'lockedNodeQPT',
      args: [userAddress]
    }).catch(() => 0n)

    return {
      nodeStaking: {
        amount: formatUnits(nodeStaking, 18),
        rawAmount: nodeStaking
      },
      roomLocks: [], // 房间锁仓记录，需要遍历房间
      note: '完整锁仓历史需要事件日志查询'
    }
  } catch (error) {
    console.error('查询QPT锁仓历史失败:', error)
    return { nodeStaking: { amount: '0', rawAmount: 0n }, roomLocks: [], note: '查询失败' }
  }
}

/**
 * 查询房间的完整参与者信息
 */
export async function queryRoomParticipants(roomId) {
  try {
    const groupBuyRoomAddress = CONTRACT_ADDRESSES[97].GroupBuyRoom

    const roomInfo = await publicClient.readContract({
      address: groupBuyRoomAddress,
      abi: ABIS.GroupBuyRoom,
      functionName: 'getRoom',
      args: [BigInt(roomId)]
    })

    if (!roomInfo) {
      throw new Error('房间不存在')
    }

    const participants = roomInfo[2] || []
    const participantDetails = []

    // 获取每个参与者的详细信息
    for (let i = 0; i < participants.length; i++) {
      const participant = participants[i]
      participantDetails.push({
        address: participant,
        joinOrder: i + 1,
        isCreator: participant.toLowerCase() === roomInfo[0].toLowerCase()
      })
    }

    return {
      roomId,
      creator: roomInfo[0],
      tier: formatUnits(roomInfo[1], 6),
      createTime: new Date(Number(roomInfo[3]) * 1000),
      isClosed: roomInfo[4],
      isSuccessful: roomInfo[5],
      participants: participantDetails,
      participantCount: participants.length
    }
  } catch (error) {
    console.error('查询房间参与者失败:', error)
    throw new Error(`查询房间参与者失败: ${error.message}`)
  }
}

/**
 * 查询用户的拼团统计数据
 */
export async function queryUserGroupBuyStats(userAddress) {
  try {
    const userRooms = await queryUserAllRooms(userAddress)
    
    const stats = {
      totalRooms: userRooms.length,
      createdRooms: userRooms.filter(room => room.isCreator).length,
      joinedRooms: userRooms.filter(room => !room.isCreator).length,
      successfulRooms: userRooms.filter(room => room.isSuccessful).length,
      failedRooms: userRooms.filter(room => room.isClosed && !room.isSuccessful).length,
      activeRooms: userRooms.filter(room => !room.isClosed).length,
      totalVolume: userRooms.reduce((sum, room) => sum + parseFloat(room.tier), 0).toFixed(2)
    }

    return {
      userAddress,
      stats,
      rooms: userRooms,
      timestamp: new Date()
    }
  } catch (error) {
    console.error('查询用户拼团统计失败:', error)
    throw new Error(`查询用户拼团统计失败: ${error.message}`)
  }
}

/**
 * 查询用户的积分余额和历史
 */
export async function queryUserPointsBalance(userAddress) {
  try {
    const pointsManagementAddress = CONTRACT_ADDRESSES[97].PointsManagement

    const balance = await publicClient.readContract({
      address: pointsManagementAddress,
      abi: ABIS.PointsManagement,
      functionName: 'balanceOf',
      args: [userAddress]
    }).catch(() => 0n)

    return {
      userAddress,
      balance: formatUnits(balance, 6), // 使用正确的6位精度
      rawBalance: balance,
      transfers: await queryUserPointsTransfers(userAddress),
      timestamp: new Date()
    }
  } catch (error) {
    console.error('查询用户积分余额失败:', error)
    throw new Error(`查询用户积分余额失败: ${error.message}`)
  }
}

export default {
  queryUserTransactionHistory,
  queryUserAllRooms,
  queryUserPointsTransfers,
  queryUserQPTLockHistory,
  queryRoomParticipants,
  queryUserGroupBuyStats,
  queryUserPointsBalance
}
