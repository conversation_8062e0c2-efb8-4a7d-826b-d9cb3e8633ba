// src/components/Admin/LockManagement/index.jsx
import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { queryContractBalances } from '@/utils/contractBalanceQueryFixed';
import { createPublicClient, http, formatUnits, parseUnits } from 'viem';
import { bscTestnet } from 'viem/chains';
import { CONTRACT_ADDRESSES, ABIS } from '@/contracts';
import './index.css';

export default function LockManagement() {
  const [lockStats, setLockStats] = useState({
    qptBalance: '0.000',
    totalDistributed: '0.000',
    bnbBalance: '0.000',
    // 分类锁仓数据
    groupBuyLock: {
      totalLocked: '0',
      activeUsers: '0',
      totalRooms: '0'
    },
    nodeStakingLock: {
      totalLocked: '0',
      activeUsers: '0',
      averageStake: '0'
    },
    qptBuybackLock: {
      totalLocked: '0',
      activeUsers: '0',
      totalRooms: '0',
      collectedQPT: '0'  // QPT收集统计
    }
  });
  const [isLoadingStats, setIsLoadingStats] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState(null);

  // 查询拼团锁仓数据 - 修复版本
  const queryGroupBuyLockData = async (publicClient, qptLockerAddress) => {
    try {
      const groupBuyRoomAddress = CONTRACT_ADDRESSES[97].GroupBuyRoom;

      // 查询总房间数
      const totalRooms = await publicClient.readContract({
        address: groupBuyRoomAddress,
        abi: ABIS.GroupBuyRoom,
        functionName: 'totalRooms'
      }).catch(() => 0n);

      // 统计拼团锁仓数据
      let totalLocked = 0n;        // 历史总锁仓
      let activeLocked = 0n;       // 当前活跃锁仓
      let claimedAmount = 0n;      // 已领取数量
      let uniqueUsers = new Set();
      let activeRooms = 0;

      // 遍历房间统计锁仓数据（限制查询数量避免RPC超时）
      // 注意：房间ID从0开始，totalRooms返回的是nextRoomId（下一个要分配的ID）
      const maxRoomsToQuery = Math.min(Number(totalRooms), 50); // 限制查询50个房间

      for (let roomId = 0; roomId < maxRoomsToQuery; roomId++) {
        try {
          // 查询房间锁仓信息
          const roomInfo = await publicClient.readContract({
            address: qptLockerAddress,
            abi: ABIS.QPTLocker,
            functionName: 'getRoomInfo',
            args: [roomId]
          });

          if (roomInfo && roomInfo[0] !== '0x0000000000000000000000000000000000000000') {
            const amount = roomInfo[1];
            const isClaimed = roomInfo[4];

            totalLocked += amount;           // 累加历史总锁仓
            uniqueUsers.add(roomInfo[0]);    // creator

            if (isClaimed) {
              claimedAmount += amount;       // 累加已领取数量
            } else {
              activeLocked += amount;        // 累加当前活跃锁仓
              activeRooms++;
            }
          }
        } catch (error) {
          // 忽略单个房间查询失败
          console.warn(`查询拼团房间 ${roomId} 锁仓信息失败:`, error.message);
        }
      }

      return {
        totalLocked,      // 历史总锁仓
        activeLocked,     // 当前活跃锁仓
        claimedAmount,    // 已领取数量
        uniqueUsers: uniqueUsers.size,
        activeRooms
      };
    } catch (error) {
      console.error('查询拼团锁仓数据失败:', error);
      return {
        totalLocked: 0n,
        activeLocked: 0n,
        claimedAmount: 0n,
        uniqueUsers: 0,
        activeRooms: 0
      };
    }
  };



  // 获取分类锁仓数据
  const loadCategorizedLockData = async () => {
    try {
      const publicClient = createPublicClient({
        chain: bscTestnet,
        transport: http()
      });

      const qptLockerAddress = CONTRACT_ADDRESSES[97].QPTLocker;
      const nodeStakingAddress = CONTRACT_ADDRESSES[97].NodeStaking;
      const queryContractAddress = CONTRACT_ADDRESSES[97].SimpleQueryContract;

      // 直接查询各个合约获取数据
      const [totalEffectiveNodes, currentRequiredStake] = await Promise.all([
        publicClient.readContract({
          address: nodeStakingAddress,
          abi: ABIS.NodeStaking,
          functionName: 'totalEffectiveNodes'
        }).catch(() => 0n),
        publicClient.readContract({
          address: nodeStakingAddress,
          abi: ABIS.NodeStaking,
          functionName: 'getCurrentRequiredStake'
        }).catch(() => 0n)
      ]);

      // 查询真实的节点质押总量 - 使用升级后的 SimpleQueryContract
      let totalNodeStaked = 0n;
      try {
        // 优先使用升级后的 SimpleQueryContract 获取准确的锁仓统计
        const lockingStats = await publicClient.readContract({
          address: queryContractAddress,
          abi: ABIS.SimpleQueryContract,
          functionName: 'getLockingStats',
          args: [50] // 查询最近50个房间
        });

        totalNodeStaked = lockingStats.totalNodeStaked;
        // 节点质押总量查询完成（移除日志输出）
      } catch (queryError) {
        console.warn('SimpleQueryContract 查询失败，使用备用方案:', queryError.message);

        try {
          // 备用方案：尝试从QPTLocker合约查询节点质押总量
          totalNodeStaked = await publicClient.readContract({
            address: qptLockerAddress,
            abi: ABIS.QPTLocker,
            functionName: 'totalNodeStaked'
          });
        } catch {
          // 最后备用方案：查询每个节点的实际锁仓数量
          try {
            // 获取所有节点地址
            const nodeAddresses = await publicClient.readContract({
              address: nodeStakingAddress,
              abi: ABIS.NodeStaking,
              functionName: 'getNodeAddresses'
            });

            // 查询每个节点的实际锁仓数量
            totalNodeStaked = 0n;
            for (const nodeAddress of nodeAddresses) {
              try {
                const lockedAmount = await publicClient.readContract({
                  address: qptLockerAddress,
                  abi: ABIS.QPTLocker,
                  functionName: 'lockedNodeQPT',
                  args: [nodeAddress]
                });
                totalNodeStaked += lockedAmount;
              } catch (error) {
                console.warn(`查询节点 ${nodeAddress} 锁仓数量失败:`, error.message);
              }
            }
          } catch (error) {
            console.warn('无法查询节点实际锁仓数量，使用默认值0:', error.message);
            totalNodeStaked = 0n;
          }
        }
      }

      // 移除调试日志，保持控制台清洁

      // 查询拼团锁仓数据
      const groupBuyData = await queryGroupBuyLockData(publicClient, qptLockerAddress);

      // 修复：groupBuyData 现在返回对象而不是数组
      const groupBuyLocked = groupBuyData.activeLocked;
      const groupBuyUsers = groupBuyData.uniqueUsers;
      const groupBuyRooms = groupBuyData.activeRooms;



      return {
        groupBuyLock: {
          totalLocked: formatUnits(groupBuyLocked, 18),
          activeUsers: groupBuyUsers.toString(),
          totalRooms: groupBuyRooms.toString()
        },
        nodeStakingLock: {
          totalLocked: formatUnits(totalNodeStaked, 18),
          activeUsers: Number(totalEffectiveNodes).toString(),
          averageStake: Number(totalEffectiveNodes) > 0 ?
            formatUnits(totalNodeStaked / totalEffectiveNodes, 18) : '0'
        }
      };
    } catch (error) {
      console.error('获取分类锁仓数据失败:', error);
      return {
        groupBuyLock: { totalLocked: '0', activeUsers: '0', totalRooms: '0' },
        nodeStakingLock: { totalLocked: '0', activeUsers: '0', averageStake: '0' }
      };
    }
  };

  // 加载锁仓统计数据
  const loadLockStats = async () => {
    setIsLoadingStats(true);
    try {
      const publicClient = createPublicClient({
        chain: bscTestnet,
        transport: http()
      });

      const qptLockerAddress = CONTRACT_ADDRESSES[97].QPTLocker;

      const [balances, categorizedData, distributionStats, bnbBalance] = await Promise.all([
        queryContractBalances(),
        loadCategorizedLockData(),
        // 获取QPT分发统计信息
        publicClient.readContract({
          address: qptLockerAddress,
          abi: ABIS.QPTLocker,
          functionName: 'getDistributionStats'
        }).catch(() => [0n, 0n, 0n]),
        // 获取BNB余额
        publicClient.getBalance({
          address: qptLockerAddress
        }).catch(() => 0n)
      ]);

      // 解构分发统计数据 [totalDistributed, totalLocked, totalClaimed]
      const [totalDistributed, totalLocked, totalClaimed] = distributionStats;

      if (balances?.qptlocker) {
        setLockStats({
          qptBalance: balances.qptlocker.qpt || '0.000',
          totalClaimed: formatUnits(totalClaimed, 18),
          bnbBalance: formatUnits(bnbBalance, 18),
          ...categorizedData
        });
        setLastUpdateTime(new Date());
      }
    } catch (error) {
      console.error('加载锁仓统计失败:', error);
      toast.error('加载锁仓统计失败: ' + error.message);
    } finally {
      setIsLoadingStats(false);
    }
  };

  // 组件挂载时加载统计数据 - 重新启用，添加延迟避免冲突
  useEffect(() => {
    // 添加1秒延迟，避免与其他组件的API请求冲突
    const timer = setTimeout(() => {
      loadLockStats();
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  // 格式化时间
  const formatTime = (date) => {
    if (!date) return '暂无数据';
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <div className="lock-management">
      <div className="management-header">
        <div className="header-content">
          <h2>🔒 锁仓管理</h2>
          <p>管理QPT锁仓和解锁</p>
        </div>
        <div className="header-actions">
          <span className="last-update">
            最后更新: {formatTime(lastUpdateTime)}
          </span>
          <button
            className="refresh-btn"
            onClick={loadLockStats}
            disabled={isLoadingStats}
          >
            {isLoadingStats ? '🔄 刷新中...' : '🔄 刷新统计'}
          </button>
        </div>
      </div>

      {/* 锁仓统计信息 */}
      <div className="lock-stats-section">
        <h3>📊 锁仓统计</h3>
        <div className="stats-grid">
          <div className="stat-card primary">
            <div className="stat-icon">🪙</div>
            <div className="stat-content">
              <div className="stat-value">{lockStats.qptBalance}</div>
              <div className="stat-label">QPT余额</div>
            </div>
          </div>
          <div className="stat-card success">
            <div className="stat-icon">📤</div>
            <div className="stat-content">
              <div className="stat-value">{lockStats.totalClaimed}</div>
              <div className="stat-label">已领取QPT</div>
            </div>
          </div>
          <div className="stat-card info">
            <div className="stat-icon">🔒</div>
            <div className="stat-content">
              <div className="stat-value">
                {(
                  parseFloat(lockStats.groupBuyLock?.totalLocked || '0') +
                  parseFloat(lockStats.nodeStakingLock?.totalLocked || '0') +
                  parseFloat(lockStats.qptBuybackLock?.totalLocked || '0')
                ).toFixed(0)}
              </div>
              <div className="stat-label">总锁仓QPT</div>
            </div>
          </div>
          <div className="stat-card warning">
            <div className="stat-icon">👥</div>
            <div className="stat-content">
              <div className="stat-value">
                {(
                  parseInt(lockStats.groupBuyLock?.activeUsers || '0') +
                  parseInt(lockStats.nodeStakingLock?.activeUsers || '0') +
                  parseInt(lockStats.qptBuybackLock?.activeUsers || '0')
                )}
              </div>
              <div className="stat-label">总锁仓用户</div>
            </div>
          </div>
          <div className="stat-card accent">
            <div className="stat-icon">⚡</div>
            <div className="stat-content">
              <div className="stat-value">{parseFloat(lockStats.bnbBalance).toFixed(3)}</div>
              <div className="stat-label">BNB (Gas费)</div>
            </div>
          </div>
        </div>
      </div>

      {/* 分类锁仓统计 */}
      <div className="lock-categories-section">
        <h3>📊 分类锁仓统计</h3>
        <p className="section-desc">按业务模块区分的锁仓数据</p>

        <div className="lock-categories">
          {/* 拼团锁仓 */}
          <div className="category-card">
            <div className="category-header">
              <div className="category-icon">🛒</div>
              <div className="category-title">拼团锁仓</div>
            </div>
            <div className="category-stats">
              <div className="category-stat">
                <div className="stat-value">{lockStats.groupBuyLock?.totalLocked || '0'}</div>
                <div className="stat-label">锁仓QPT</div>
              </div>
              <div className="category-stat">
                <div className="stat-value">{lockStats.groupBuyLock?.activeUsers || '0'}</div>
                <div className="stat-label">参与用户</div>
              </div>
              <div className="category-stat">
                <div className="stat-value">{lockStats.groupBuyLock?.totalRooms || '0'}</div>
                <div className="stat-label">活跃房间</div>
              </div>
            </div>
          </div>

          {/* 节点质押锁仓 */}
          <div className="category-card">
            <div className="category-header">
              <div className="category-icon">🏗️</div>
              <div className="category-title">节点质押锁仓</div>
            </div>
            <div className="category-stats">
              <div className="category-stat">
                <div className="stat-value">{lockStats.nodeStakingLock?.totalLocked || '0'}</div>
                <div className="stat-label">锁仓QPT</div>
              </div>
              <div className="category-stat">
                <div className="stat-value">{lockStats.nodeStakingLock?.activeUsers || '0'}</div>
                <div className="stat-label">质押用户</div>
              </div>
              <div className="category-stat">
                <div className="stat-value">{parseFloat(lockStats.nodeStakingLock?.averageStake || '0').toFixed(0)}</div>
                <div className="stat-label">平均质押</div>
              </div>
            </div>
          </div>


        </div>
      </div>

      {/* 合约信息 */}
      <div className="contract-info-section">
        <h3>📋 合约信息</h3>
        <div className="contract-details">
          <div className="detail-item">
            <span className="detail-label">合约名称：</span>
            <span className="detail-value">QPTLocker</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">合约类型：</span>
            <span className="detail-value">锁仓合约</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">锁仓期限：</span>
            <span className="detail-value">灵活锁仓</span>
          </div>
          <div className="detail-item">
            <span className="detail-label">解锁方式：</span>
            <span className="detail-value">按需解锁</span>
          </div>
        </div>
      </div>

      {/* 锁仓操作 */}
      <div className="lock-operations">
        <h3>⚡ 锁仓操作</h3>
        <div className="operation-buttons">
          <button className="operation-btn primary">
            🔒 查看锁仓记录
          </button>
          <button className="operation-btn success">
            📊 导出锁仓报告
          </button>
          <button className="operation-btn info">
            🔍 查询用户锁仓
          </button>
          <button className="operation-btn warning">
            ⚙️ 锁仓设置
          </button>
        </div>
      </div>
    </div>
  );
}
