// src/components/Admin/PointsManagement/index.jsx
import { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { queryContractBalances, querySystemOverview } from '@/utils/contractBalanceQueryFixed';
import './index.css';

export default function PointsManagement() {
  const [pointsStats, setPointsStats] = useState({
    usdtBalance: '0.000',
    bnbBalance: '0.000',
    totalSupply: '0',
    groupBuyPoints: '0',
    salesPoints: '0',
    dailyGroupBuyPointsIssued: '0',
    dailySalesPointsIssued: '0',
    dailyPointsTransfers: '0',
    dailyPointsExchanged: '0',
    dailyPointsBurned: '0'
  });
  const [isLoadingStats, setIsLoadingStats] = useState(false);
  const [lastUpdateTime, setLastUpdateTime] = useState(null);

  // 加载积分统计数据
  const loadPointsStats = async () => {
    setIsLoadingStats(true);
    try {
      // 优先使用查询合约优化服务获取完整的积分统计数据
      let pointsStatsData = {};
      let optimizedDailyStats = null;

      try {
        const { getPointsSystemStatsFromQueryContract, getOptimizedDailyPointsStatsFromQueryContract } = await import('@/utils/queryContractService');

        // 获取完整的积分系统统计数据
        pointsStatsData = await getPointsSystemStatsFromQueryContract();

        // 获取每日积分统计数据
        optimizedDailyStats = await getOptimizedDailyPointsStatsFromQueryContract();
      } catch (queryError) {
        console.warn('查询合约服务失败，使用传统方法:', queryError);
        // 查询合约服务失败，使用传统方法
        const overview = await querySystemOverview();
        pointsStatsData = overview?.pointsStats || {};
      }

      // 获取合约余额信息
      const balances = await queryContractBalances();
      const pointsBalance = balances?.pointsManagement || {};

      // 使用优化的每日统计数据（如果可用）
      const dailyPointsStats = optimizedDailyStats || {};

      setPointsStats({
        usdtBalance: pointsBalance.usdt || pointsStatsData.usdtBalance || '0.000',
        bnbBalance: pointsBalance.bnb || '0.000',
        totalSupply: pointsStatsData.totalSupply || '0',
        groupBuyPoints: pointsStatsData.groupBuyPoints || '0',
        salesPoints: pointsStatsData.salesPoints || '0',
        burnedPoints: pointsStatsData.burnedPoints || '0',
        usdtExchanged: pointsStatsData.usdtExchanged || '0',
        dailyGroupBuyPointsIssued: dailyPointsStats.dailyGroupBuyPointsIssued || '0',
        dailySalesPointsIssued: dailyPointsStats.dailySalesPointsIssued || '0',
        dailyPointsTransfers: dailyPointsStats.dailyPointsTransfers || '0',
        dailyPointsExchanged: dailyPointsStats.dailyPointsExchanged || '0',
        dailyPointsBurned: dailyPointsStats.dailyPointsBurned || '0',
        dataSource: pointsStatsData.source || dailyPointsStats.source || 'traditional',
        queryOptimized: !!optimizedDailyStats
      });

      setLastUpdateTime(new Date());
    } catch (error) {
      console.error('加载积分统计失败:', error);
      toast.error('加载积分统计失败: ' + error.message);
    } finally {
      setIsLoadingStats(false);
    }
  };

  // 组件挂载时加载统计数据
  useEffect(() => {
    loadPointsStats();
  }, []);

  // 格式化时间
  const formatTime = (date) => {
    if (!date) return '暂无数据';
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <div className="points-management">
      <div className="management-header">
        <div className="header-content">
          <h2>🎯 积分管理</h2>
          <p>管理平台积分系统和兑换</p>
        </div>
        <div className="header-actions">
          <span className="last-update">
            最后更新: {formatTime(lastUpdateTime)}
          </span>
          <button
            className="refresh-btn"
            onClick={loadPointsStats}
            disabled={isLoadingStats}
          >
            {isLoadingStats ? '🔄 刷新中...' : '🔄 刷新统计'}
          </button>
        </div>
      </div>

      {/* 积分统计信息 */}
      <div className="points-stats-section">
        <h3>📊 积分统计</h3>
        <div className="stats-grid">
          <div className="stat-card primary">
            <div className="stat-icon">💵</div>
            <div className="stat-content">
              <div className="stat-value">{pointsStats.usdtBalance}</div>
              <div className="stat-label">USDT余额</div>
            </div>
          </div>
          <div className="stat-card success">
            <div className="stat-icon">📊</div>
            <div className="stat-content">
              <div className="stat-value">{pointsStats.totalSupply}</div>
              <div className="stat-label">积分总量</div>
            </div>
          </div>
          <div className="stat-card info">
            <div className="stat-icon">🏪</div>
            <div className="stat-content">
              <div className="stat-value">{pointsStats.groupBuyPoints}</div>
              <div className="stat-label">拼团积分</div>
            </div>
          </div>
          <div className="stat-card warning">
            <div className="stat-icon">💼</div>
            <div className="stat-content">
              <div className="stat-value">{pointsStats.salesPoints}</div>
              <div className="stat-label">销售积分</div>
            </div>
          </div>
          <div className="stat-card danger">
            <div className="stat-icon">🔥</div>
            <div className="stat-content">
              <div className="stat-value">{pointsStats.burnedPoints}</div>
              <div className="stat-label">积分销毁</div>
            </div>
          </div>
          <div className="stat-card secondary">
            <div className="stat-icon">💰</div>
            <div className="stat-content">
              <div className="stat-value">{pointsStats.usdtExchanged}</div>
              <div className="stat-label">已兑换USDT</div>
            </div>
          </div>
          <div className="stat-card accent">
            <div className="stat-icon">⚡</div>
            <div className="stat-content">
              <div className="stat-value">{parseFloat(pointsStats.bnbBalance).toFixed(3)}</div>
              <div className="stat-label">BNB (Gas费)</div>
            </div>
          </div>
        </div>
      </div>

      {/* 今日积分统计 */}
      <div className="daily-stats-section">
        <h3>📈 今日积分统计</h3>
        <div className="daily-grid">
          <div className="daily-card positive">
            <div className="daily-icon">🏪</div>
            <div className="daily-content">
              <div className="daily-value">{pointsStats.dailyGroupBuyPointsIssued}</div>
              <div className="daily-label">拼团积分发放</div>
            </div>
          </div>
          <div className="daily-card info">
            <div className="daily-icon">💼</div>
            <div className="daily-content">
              <div className="daily-value">{pointsStats.dailySalesPointsIssued}</div>
              <div className="daily-label">销售积分发放</div>
            </div>
          </div>
          <div className="daily-card warning">
            <div className="daily-icon">🔄</div>
            <div className="daily-content">
              <div className="daily-value">{pointsStats.dailyPointsTransfers}</div>
              <div className="daily-label">积分转账次数</div>
            </div>
          </div>
          <div className="daily-card success">
            <div className="daily-icon">💰</div>
            <div className="daily-content">
              <div className="daily-value">{pointsStats.dailyPointsExchanged}</div>
              <div className="daily-label">积分兑换USDT</div>
            </div>
          </div>
          <div className="daily-card negative">
            <div className="daily-icon">🔥</div>
            <div className="daily-content">
              <div className="daily-value">{pointsStats.dailyPointsBurned}</div>
              <div className="daily-label">积分销毁</div>
            </div>
          </div>
        </div>
      </div>

      {/* 积分操作 */}
      <div className="points-operations">
        <h3>⚡ 积分操作</h3>
        <div className="operation-buttons">
          <button className="operation-btn primary">
            📊 查看积分记录
          </button>
          <button className="operation-btn success">
            💰 积分兑换管理
          </button>
          <button className="operation-btn info">
            🔍 查询用户积分
          </button>
          <button className="operation-btn warning">
            ⚙️ 积分设置
          </button>
          <button className="operation-btn secondary">
            📈 积分报告
          </button>
          <button className="operation-btn accent">
            🔥 积分销毁
          </button>
        </div>
      </div>
    </div>
  );
}
