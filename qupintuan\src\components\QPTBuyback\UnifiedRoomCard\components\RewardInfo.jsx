// src/components/QPTBuyback/UnifiedRoomCard/components/RewardInfo.jsx
// QPT回购房间奖励信息组件

import React from 'react'

const RewardInfo = ({
  room,
  account,
  userRole,
  isWinner,
  winnerRewardStatus,
  refundStatus,
  qptVerification,
  refundInfo,
  onClaimWinnerReward,
  onClaimReward,
  handleRefund,
  isClaimingReward,
  isRefunding,
  calculateWinnerReward,
  isAdminView
}) => {


  // 只有参与者才显示奖励信息，但管理员视图中始终显示
  if (userRole !== 'participant' && !isAdminView) {
    return null
  }

  // 检查是否开奖完成
  const isStep2Complete = room.winner && room.winner !== '0x0000000000000000000000000000000000000000'
  const isStep1Complete = room.readyForWinner || room.lockedBuybackAmount > 0

  // 参与者视图：只有第2步完成后才显示奖励详情
  // 管理员视图：第1步完成后就可以显示奖励详情
  if (isAdminView) {
    // 管理员视图：第1步完成后就显示奖励详情
    if (!isStep1Complete) {
      return null
    }
  } else {
    // 参与者视图：只有第2步完成后才显示奖励详情
    if (!isStep2Complete) {
      return null
    }
  }

  // 获取赢家地址和奖励信息
  // 优先从多个来源获取赢家地址：第2步确认的winner > 第1步计算的winner > 开奖信息中的winner
  const winner = room.winner ||
                 room.calculatedWinner?.address ||
                 room.calculatedWinner?.winnerAddress ||
                 room.lotteryInfo?.winner ||
                 room.winnerInfo?.address ||
                 room.winnerInfo?.winnerAddress

  // 计算奖励信息 - 优先使用缓存的代理信息
  const winnerRewardInfo = (() => {
    if (!winner || !calculateWinnerReward) return null

    // 如果有缓存的代理信息，直接使用
    if (room.calculatedWinner?.agentInfo && room.calculatedWinner.address === winner) {
      const agentInfo = room.calculatedWinner.agentInfo
      const rewardAmount = Number(room.lockedBuybackAmount) * agentInfo.rewardPercent / 100

      return {
        percent: agentInfo.rewardPercent,
        amount: rewardAmount / 1e6, // 转换为USDT
        level: agentInfo.level
      }
    }

    // 否则使用calculateWinnerReward函数
    return calculateWinnerReward(winner)
  })()

  // 如果没有赢家地址且没有锁定的回购池金额，则不显示
  if (!winner && !room.lockedBuybackAmount) {
    return null
  }

  return (
    <div style={{
      background: 'rgba(34, 197, 94, 0.1)',
      border: '1px solid rgba(34, 197, 94, 0.2)',
      padding: '12px',
      borderRadius: '6px',
      marginTop: '8px'
    }}>
      <div style={{ fontWeight: 'bold', marginBottom: '8px', color: '#059669' }}>
        🎁 赢家奖励详情
        {!isStep2Complete && isAdminView && (
          <span style={{
            marginLeft: '8px',
            fontSize: '12px',
            color: '#f59e0b',
            fontWeight: 'normal'
          }}>
            (第1步已完成，等待第2步确认)
          </span>
        )}
      </div>

      {/* 回购池锁定金额 */}
      <div style={{ marginBottom: '4px' }}>
        <strong>💰 回购池锁定金额：</strong>
        <span style={{
          marginLeft: '8px',
          fontWeight: 'bold',
          color: '#dc2626'
        }}>
          {(Number(room.lockedBuybackAmount) / 1e6).toFixed(2)} USDT
        </span>
      </div>

      {/* 赢家奖励信息 */}
      {(() => {
        // 如果有具体的赢家奖励信息，显示具体信息
        if (winnerRewardInfo) {
          return (
            <>
              <div style={{ marginBottom: '4px' }}>
                <strong>⭐ 代理等级：</strong>
                <span style={{ marginLeft: '8px', color: '#059669' }}>
                  {winnerRewardInfo.level}级
                </span>
              </div>

              <div style={{ marginBottom: '4px' }}>
                <strong>📈 奖励比例：</strong>
                <span style={{ marginLeft: '8px', color: '#059669' }}>
                  {winnerRewardInfo.percent}%
                </span>
              </div>

              <div style={{ marginBottom: '8px' }}>
                <strong>💵 奖励金额：</strong>
                <span style={{
                  marginLeft: '8px',
                  fontWeight: 'bold',
                  color: '#dc2626',
                  fontSize: '16px'
                }}>
                  {winnerRewardInfo.amount.toFixed(2)} USDT
                </span>
              </div>
            </>
          )
        }

        // 如果没有具体赢家信息但有锁定金额，显示查询状态
        if (room.lockedBuybackAmount && room.lockedBuybackAmount > 0) {
          return (
            <div style={{
              marginTop: '8px',
              padding: '12px',
              background: 'rgba(255, 193, 7, 0.1)',
              border: '1px solid rgba(255, 193, 7, 0.3)',
              borderRadius: '8px',
              fontSize: '14px',
              color: '#856404'
            }}>
              🔍 正在查询赢家代理等级信息...
            </div>
          )
        }

        return null
      })()}

      {/* QPT锁仓验证状态 */}
      {qptVerification && (
        (isWinner && !winnerRewardStatus?.isAlreadyClaimed) ||
        (!isWinner && !refundStatus?.isAlreadyRefunded)
      ) && (
        <div style={{ marginTop: '8px' }}>
          <p className={`verification-status ${qptVerification.isValid ? 'valid' : 'invalid'}`}>
            {qptVerification.message}
          </p>
        </div>
      )}

      {/* 赢家领取USDT奖励按钮 - 管理员视图中不显示操作按钮 */}
      {isWinner && !isAdminView && (() => {
        // 如果没有奖励状态数据，显示查询中
        if (!winnerRewardStatus) {
          return (
            <div style={{
              marginTop: '12px',
              padding: '12px',
              background: 'rgba(255, 193, 7, 0.1)',
              border: '1px solid rgba(255, 193, 7, 0.3)',
              borderRadius: '8px',
              fontSize: '14px',
              color: '#856404'
            }}>
              查询奖励状态中...
            </div>
          )
        }

        // 检查是否已经领取
        if (winnerRewardStatus.isAlreadyClaimed) {
          return (
            <button
              disabled={true}
              title="您已成功领取奖励"
              style={{
                marginTop: '12px',
                width: '100%',
                padding: '12px',
                backgroundColor: '#6c757d',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '16px',
                fontWeight: '600',
                cursor: 'not-allowed',
                opacity: 0.7
              }}
            >
              ✅ 已领取
            </button>
          )
        }

        // 检查是否可以领取
        if (!winnerRewardStatus.canClaim) {
          return (
            <div style={{
              marginTop: '12px',
              padding: '12px',
              background: 'rgba(239, 68, 68, 0.1)',
              border: '1px solid rgba(239, 68, 68, 0.3)',
              borderRadius: '8px',
              fontSize: '14px',
              color: '#dc2626'
            }}>
              {winnerRewardStatus.reason || '暂不可领取奖励'}
            </div>
          )
        }

        // 可以领取的按钮
        return (
          <button
            onClick={() => onClaimWinnerReward?.(room)}
            disabled={isClaimingReward}
            title={isClaimingReward ? '处理中，请稍候...' : `领取 ${(winnerRewardStatus.rewardAmount || 0).toFixed(2)} USDT 奖励`}
            style={{
              marginTop: '12px',
              width: '100%',
              padding: '12px',
              backgroundColor: isClaimingReward ? '#6c757d' : '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '16px',
              fontWeight: '600',
              cursor: isClaimingReward ? 'not-allowed' : 'pointer',
              opacity: isClaimingReward ? 0.7 : 1
            }}
          >
            {isClaimingReward ? '领取中...' : `🏆 领取 ${(winnerRewardStatus.rewardAmount || 0).toFixed(2)} USDT 奖励`}
          </button>
        )
      })()}

      {/* 参与者领取QPT本金+补贴按钮 - 管理员视图中不显示操作按钮 */}
      {!isWinner && !isAdminView && (() => {
        if (!refundStatus) {
          return (
            <div style={{
              marginTop: '12px',
              padding: '12px',
              background: 'rgba(255, 193, 7, 0.1)',
              border: '1px solid rgba(255, 193, 7, 0.3)',
              borderRadius: '8px',
              fontSize: '14px',
              color: '#856404'
            }}>
              查询状态中...
            </div>
          )
        }

        if (refundStatus.isAlreadyRefunded) {
          return (
            <button
              disabled={true}
              title="您已成功领取QPT奖励"
              style={{
                marginTop: '12px',
                width: '100%',
                padding: '12px',
                backgroundColor: '#6c757d',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '16px',
                fontWeight: '600',
                cursor: 'not-allowed',
                opacity: 0.7
              }}
            >
              ✅ 已领取
            </button>
          )
        }

        if (!refundInfo?.canRefund) {
          return (
            <div style={{
              marginTop: '12px',
              padding: '12px',
              background: 'rgba(239, 68, 68, 0.1)',
              border: '1px solid rgba(239, 68, 68, 0.3)',
              borderRadius: '8px',
              fontSize: '14px',
              color: '#dc2626'
            }}>
              {refundInfo?.reason || '暂不可领取QPT'}
            </div>
          )
        }

        return (
          <button
            onClick={handleRefund}
            disabled={isRefunding}
            title={isRefunding ? '处理中，请稍候...' : `领取 ${refundInfo.totalAmount.toFixed(2)} QPT (本金+补贴)`}
            style={{
              marginTop: '12px',
              width: '100%',
              padding: '12px',
              backgroundColor: isRefunding ? '#6c757d' : '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '8px',
              fontSize: '16px',
              fontWeight: '600',
              cursor: isRefunding ? 'not-allowed' : 'pointer',
              opacity: isRefunding ? 0.7 : 1
            }}
          >
            {isRefunding ? '领取中...' : `💎 领取 ${refundInfo.totalAmount.toFixed(2)} QPT (本金+补贴)`}
          </button>
        )
      })()}
    </div>
  )
}

export default RewardInfo
