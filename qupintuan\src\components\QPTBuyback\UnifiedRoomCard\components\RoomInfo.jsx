// src/components/QPTBuyback/UnifiedRoomCard/components/RoomInfo.jsx
// QPT回购房间信息组件 - 简洁格式

import React from 'react'
import { formatAddress } from '@/utils/addressFormatter'
import ParticipantsList from './ParticipantsList'

const RoomInfo = ({ room, realTierAmount, account }) => {
  // 获取档位显示名称
  const getTierDisplayName = (tier) => {
    const tierMap = {
      0: '100 QPT',
      1: '200 QPT',
      2: '500 QPT',
      3: '1000 QPT'
    }
    return tierMap[tier] !== undefined ? tierMap[tier] : '100 QPT' // 默认100 QPT
  }

  // 获取最终显示的档位
  const getFinalTierDisplay = () => {
    if (realTierAmount) {
      return `${realTierAmount} QPT`
    }
    if (room.tier !== undefined) {
      return getTierDisplayName(room.tier)
    }
    return '100 QPT' // 默认值
  }

  // 格式化倒计时
  const formatCountdown = (deadline) => {
    if (!deadline) return '已结束'
    
    const now = Math.floor(Date.now() / 1000)
    const timeLeft = deadline - now
    
    if (timeLeft <= 0) return '已结束'
    
    const hours = Math.floor(timeLeft / 3600)
    const minutes = Math.floor((timeLeft % 3600) / 60)
    
    if (hours > 0) {
      return `${hours}小时${minutes}分钟`
    } else {
      return `${minutes}分钟`
    }
  }

  // 格式化创建时间 - 显示具体时间（包含年份和秒）
  const formatCreateTime = (timestamp) => {
    if (!timestamp) return '未知时间'
    const date = new Date(timestamp * 1000)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).replace(/\//g, '/')
  }

  return (
    <div className="room-info">
      {/* 发起人 */}
      <div className="info-row">
        <span className="label">发起人：</span>
        <span className="value creator-address" title={room.creator}>
          {formatAddress(room.creator || '0x0000000000000000000000000000000000000000')}
        </span>
      </div>

      {/* 档位 */}
      <div className="info-row">
        <span className="label">档位：</span>
        <span className="value tier-amount">
          {getFinalTierDisplay()}
        </span>
      </div>

      {/* 参与人数 */}
      <div className="info-row">
        <span className="label">参与：</span>
        <span className="value participants">
          {room.participantsCount || 0} / 8
        </span>
      </div>

      {/* 参与者列表（只要有参与者就显示） */}
      <ParticipantsList room={room} account={account} />

      {/* 状态显示 - 完成第2步开奖后不显示倒计时 */}
      {(() => {
        // 检查是否已完成第2步开奖
        const isStep2Complete = room.winner && room.winner !== '0x0000000000000000000000000000000000000000'

        if (isStep2Complete) {
          // 完成第2步开奖后显示"已完成"状态，不显示倒计时
          return (
            <div className="info-row">
              <span className="label">状态：</span>
              <span className="value status-complete" style={{ color: '#059669', fontWeight: 'bold' }}>
                ✅ 已完成
              </span>
            </div>
          )
        } else {
          // 未完成第2步开奖时显示倒计时（带背景色和闪烁效果）
          const now = Math.floor(Date.now() / 1000)
          const timeLeft = room.deadline - now
          const isUrgent = timeLeft <= 3600 // 最后1小时

          return (
            <div className="info-row">
              <span className="label">倒计时：</span>
              <span className="value countdown-wrapper">
                <span className={`room-countdown ${isUrgent ? 'urgent' : ''}`}>
                  {formatCountdown(room.deadline)}
                </span>
              </span>
            </div>
          )
        }
      })()}

      {/* 创建时间 */}
      <div className="info-row">
        <span className="label">创建：</span>
        <span className="value create-time">
          {formatCreateTime(room.createdAt)}
        </span>
      </div>
    </div>
  )
}

export default RoomInfo
